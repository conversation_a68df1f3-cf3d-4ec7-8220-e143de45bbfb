import os
import csv
import time
import numpy as np
import pandas as pd
from datetime import datetime
from hyperopt import fmin, tpe, hp, Trials, STATUS_OK
from BT_Thursday_MainCode import run_for_one_day
from Matrix import *
from dotenv import load_dotenv
from load_parquet_in_memory import load_parquet_data, list_date_folders
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from functools import partial


"""
Parameter            Old Range           New (Broader) Range
stop_loss           -0.5 to -1.5        -0.3 to -2.0
target_profit       1.0 to 2.0          0.5 to 3.0
ratio_check         0.1 to 0.2          0.05 to 0.30
resize_factor       0.3 to 0.7          0.1 to 1.0
max_evals           50                  100
exit_time           14:45 to 15:15      14:00 to 15:30
start_time          09:30 to 09:55      09:15 to 10:55
"""

load_dotenv()

# Parse env only once
def _get_env_int(name: str, default: int = 0) -> int:
    val = os.getenv(name)
    try:
        return int(val) if val is not None and val != "" else default
    except Exception:
        return default

def _get_env_float(name: str, default: float = 0.0) -> float:
    val = os.getenv(name)
    try:
        return float(val) if val is not None and val != "" else default
    except Exception:
        return default

CAPITAL = _get_env_int("Capital", 0)
MAX_EVALS = _get_env_int("MAX_EVALS", 50)
# Prune trials early if drawdown exceeds this threshold during evaluation
EARLY_MDD_CUTOFF = _get_env_float("EARLY_MDD_CUTOFF", 0.35)  # adjust as needed
# Risk penalty settings
MDD_THRESHOLD = _get_env_float("MDD_THRESHOLD", 0.09)
RISK_GAMMA = _get_env_float("RISK_GAMMA", 15.0)
RISK_EXP_CAP = _get_env_float("RISK_EXP_CAP", 50.0)
# Multithreading settings
MAX_WORKERS = _get_env_int("MAX_WORKERS", min(32, (os.cpu_count() or 1) + 4))  # Default to reasonable thread count

def build_results_csv_path():
    """
    Build the CSV results path using WINDOW_NO and MAX_EVALS from .env.
    Output file: PT_Results/Window{WINDOW_NO}_{MAX_EVALS}.csv
    Falls back to 'NA' when env vars are missing.
    """
    os.makedirs("PT_Results", exist_ok=True)
    window_no = os.getenv("WINDOW_NO") or "NA"
    max_evals_env = os.getenv("MAX_EVALS") or "NA"
    filename = f"Window{window_no}_{max_evals_env}.csv"
    return os.path.join("PT_Results", filename)


# ------------------ Helper to Remove Duplicate Trials ------------------ #
def get_unique_sorted_trials(trials):
    seen = set()
    unique_trials = []
    for trial in sorted(trials.results, key=lambda x: x['loss']):
        param_tuple = tuple(sorted(trial['params'].items()))
        if param_tuple not in seen:
            seen.add(param_tuple)
            unique_trials.append(trial)
    return unique_trials


# ------------------ Save ALL Unique Trials to CSV ------------------ #
def save_results_to_csv(trials, file_path):
    seen = set()
    unique_rows = []
    capital = CAPITAL  # use pre-parsed

    for trial in trials.results:
        params = trial['params']
        param_tuple = tuple(sorted(params.items()))
        if param_tuple not in seen:
            seen.add(param_tuple)
            total_pnl = trial.get('total_pnl', 0.0)
            max_drawdown = trial.get('max_drawdown', 0.0)
            normalized_pnl = (total_pnl / capital) if capital else 0.0
            risk_penalty = trial.get('risk_penalty', 1.0)

            unique_rows.append({
                'start_time': params['start_time'],
                'exit_time': params['exit_time'],
                'stop_loss': params['stop_loss'],
                'target_profit': params['target_profit'],
                'ratio_check': params['ratio_check'],
                'resize_factor': params['resize_factor'],
                'total_pnl': total_pnl,
                'normalized_pnl': normalized_pnl,
                'max_drawdown': max_drawdown,
                'risk_penalty': risk_penalty,
                'final_score': -trial.get('loss', 0.0)
            })

    unique_rows.sort(key=lambda x: x['final_score'], reverse=True)

    with open(file_path, mode='w', newline='') as file:
        writer = csv.DictWriter(file, fieldnames=[
            'start_time', 'exit_time', 'stop_loss', 'target_profit',
            'ratio_check', 'resize_factor', 'total_pnl', 'normalized_pnl',
            'max_drawdown', 'risk_penalty', 'final_score'
        ])
        writer.writeheader()
        writer.writerows(unique_rows)


# ------------------ Fast max drawdown on numpy ------------------ #
def max_drawdown_from_pnl(pnl_array: np.ndarray) -> float:
    """
    Compute max drawdown from a series of daily PnL values.
    Returns drawdown as a fraction (0.0..1.0). Handles non-positive peaks.
    """
    if pnl_array.size == 0:
        return 0.0
    equity = np.cumsum(pnl_array, dtype=np.float64)
    peaks = np.maximum.accumulate(equity)
    # Avoid div by zero; where peak <= 0, define dd as 0
    with np.errstate(divide='ignore', invalid='ignore'):
        dd = np.where(peaks > 0, (peaks - equity) / peaks, 0.0)
    return float(np.max(dd)) if dd.size else 0.0


# ------------------ Thread-safe wrapper for run_for_one_day ------------------ #
def run_single_day_wrapper(mainkey, subfolders, start_time, exit_time, 
                          stop_loss, target_profit, ratio_check, resize_factor):
    """
    Thread-safe wrapper for run_for_one_day that handles exceptions and returns consistent results.
    """
    try:
        result = run_for_one_day(
            mainkey, subfolders, start_time, exit_time,
            stop_loss, target_profit, ratio_check, resize_factor
        )
        if result[0] is None or result[1] is None:
            return None
        return result
    except Exception as e:
        # Log error if needed, but don't crash the entire optimization
        print(f"Error processing {mainkey}: {str(e)}")
        return None

# ------------------ Multithreaded Objective Function ------------------ #
def objective(params, data):
    # Round sensitive continuous parameters (stable bins help TPE)
    params['stop_loss'] = round(float(params['stop_loss']), 2)
    params['target_profit'] = round(float(params['target_profit']), 2)
    params['ratio_check'] = round(float(params['ratio_check']), 3)
    params['resize_factor'] = round(float(params['resize_factor']), 2)

    # Parse times only once per trial
    start_time = datetime.strptime(params['start_time'], "%H:%M").time()
    exit_time = datetime.strptime(params['exit_time'], "%H:%M").time()
    stop_loss = params['stop_loss']
    target_profit = params['target_profit']
    ratio_check = params['ratio_check']
    resize_factor = params['resize_factor']

    # Prepare data items for parallel processing
    data_items = list(data.items())
    
    # Use ThreadPoolExecutor for parallel processing of days
    daily_pnls = []
    running_equity = 0.0
    running_peak = 0.0
    running_mdd = 0.0
    
    # Create a partial function with fixed parameters
    process_day = partial(
        run_single_day_wrapper,
        start_time=start_time,
        exit_time=exit_time,
        stop_loss=stop_loss,
        target_profit=target_profit,
        ratio_check=ratio_check,
        resize_factor=resize_factor
    )
    
    # Process days in parallel with controlled batch size to manage memory
    batch_size = min(MAX_WORKERS * 2, len(data_items))  # Process in batches
    
    for i in range(0, len(data_items), batch_size):
        batch = data_items[i:i + batch_size]
        
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # Submit all tasks in the batch
            future_to_data = {
                executor.submit(process_day, mainkey, subfolders): (mainkey, subfolders)
                for mainkey, subfolders in batch
            }
            
            # Collect results as they complete
            batch_results = []
            for future in as_completed(future_to_data):
                result = future.result()
                if result is not None:
                    batch_results.append(result)
            
            # Sort results by date to maintain chronological order for drawdown calculation
            batch_results.sort(key=lambda x: x[0])  # Sort by date_str
            
            # Process results in chronological order for accurate drawdown tracking
            for date_str, pnl, exit_reason, vix_close_value in batch_results:
                if pnl is not None:
                    pnl_val = float(pnl)
                    daily_pnls.append(pnl_val)

                    # Update running equity and mdd fast; may prune bad trials early
                    running_equity += pnl_val
                    if running_equity > running_peak:
                        running_peak = running_equity
                    if running_peak > 0:
                        dd = (running_peak - running_equity) / running_peak
                        if dd > running_mdd:
                            running_mdd = dd

                    # Early pruning: if drawdown is way beyond acceptable, bail out
                    if EARLY_MDD_CUTOFF > 0 and running_mdd > EARLY_MDD_CUTOFF:
                        # No need to simulate remaining days; this trial is too risky
                        print(f"Early pruning triggered: MDD {running_mdd:.3f} > {EARLY_MDD_CUTOFF}")
                        break
        
        # Check if early pruning was triggered
        if EARLY_MDD_CUTOFF > 0 and running_mdd > EARLY_MDD_CUTOFF:
            break

    pnl_arr = np.array(daily_pnls, dtype=np.float64)
    total_pnl = float(np.sum(pnl_arr)) if pnl_arr.size else 0.0

    # Fast MDD on numpy instead of building DataFrame and calling Matrix
    max_drawdown = max_drawdown_from_pnl(pnl_arr)

    capital = CAPITAL
    normalized_pnl = (total_pnl / capital) if capital else 0.0

    # Risk penalty with exponent cap to avoid overflow
    if max_drawdown > MDD_THRESHOLD:
        penalty_input = min(RISK_GAMMA * (max_drawdown - MDD_THRESHOLD), RISK_EXP_CAP)
        risk_penalty = float(np.exp(penalty_input) + 1.0)
    else:
        risk_penalty = 1.0

    final_score = normalized_pnl / risk_penalty

    return {
        'loss': -final_score,
        'status': STATUS_OK,
        'params': params,
        'total_pnl': total_pnl,
        'max_drawdown': max_drawdown,
        'risk_penalty': risk_penalty
    }




# ------------------ Main Optimization Routine ------------------ #
def run_hyperopt():
    start_time_main = time.time()
    root_path = "/home/<USER>/AWS_Thursday_PT_V2/Parquet_Files/Thursday_output_folder"

    # Retrieve start and end dates from environment variables
    start_date_str = os.getenv("START_DATE")
    end_date_str = os.getenv("END_DATE")
    if not start_date_str or not end_date_str:
        print("START_DATE/END_DATE not set.")
        return

    try:
        from_date = datetime.strptime(start_date_str, "%Y%m%d")
        to_date = datetime.strptime(end_date_str, "%Y%m%d")
    except ValueError:
        print("Invalid START_DATE/END_DATE format. Use YYYYMMDD.")
        return

    # Identify folders and preload parquet into memory once
    all_folders = list_date_folders(root_path, from_date, to_date)
    data = load_parquet_data(all_folders)

    print(f"Loaded data for {len(data)} days")
    print(f"Using {MAX_WORKERS} threads for parallel processing")

    # Search space
    space = {
        'start_time': hp.choice('start_time', [f"{h:02d}:{m:02d}" for h in range(9, 11) for m in range(0, 60, 5) if not (h == 9 and m < 15)]),
        'exit_time': hp.choice('exit_time', [f"{h:02d}:{m:02d}" for h in range(14, 16) for m in range(0, 60, 5) if not (h == 15 and m > 29)]),
        'stop_loss': hp.quniform('stop_loss', -2.0, -0.3, 0.05),
        'target_profit': hp.quniform('target_profit', 0.5, 3.0, 0.1),
        'ratio_check': hp.loguniform('ratio_check', np.log(0.01), np.log(0.3)),
        'resize_factor': hp.loguniform('resize_factor', np.log(0.1), np.log(1.0))
    }

    trials = Trials()
    best = fmin(
        fn=lambda p: objective(p, data),
        space=space,
        algo=tpe.suggest,
        max_evals=MAX_EVALS,
        trials=trials,
        rstate=np.random.default_rng(_get_env_int("HYPEROPT_SEED", 42))  # reproducibility
    )

    print(f"Best Parameters Index: {best}")

    # Save all unique results
    csv_path = build_results_csv_path()
    save_results_to_csv(trials, csv_path)

    # Top 2 unique trials
    top_trials = get_unique_sorted_trials(trials)[:2]

    print("\nTop 2 Best Parameters:")
    for i, trial in enumerate(top_trials):
        params = trial['params']
        print(f"\nRank {i+1}:")
        for param, value in params.items():
            print(f"{param}: {value}")
        print(f"Total PnL: {trial.get('total_pnl', 0)}")
        print(f"Max Drawdown: {trial.get('max_drawdown', 0)}")
        print(f"Final Score: {-trial.get('loss', 0)}")


# ------------------ Entry Point ------------------ #
if __name__ == "__main__":
    print("Starting hyperparameter optimization...")
    start = time.time()
    run_hyperopt()
    total_execution_time = time.time() - start
    formated_total_execution_time = time.strftime("%H:%M:%S", time.gmtime(total_execution_time))
    print(f"Total execution time: {formated_total_execution_time}")